/**
 * QuestionDataConverter 类的测试文件
 */
import { describe, expect, it } from 'vitest'
import { QuestionDataConverter } from '../question-data-converter'

describe('QuestionDataConverter', () => {
  // 测试数据
  const mockApiData: Question.QuestionData = {
    Question: {
      QuestionType: '单选题',
      QuestionTypeId: '2',
      Title: '以下哪个是JavaScript的数据类型？',
      Options: [
        { Content: 'string', Option: 'A' },
        { Content: 'number', Option: 'B' },
        { Content: 'boolean', Option: 'C' },
        { Content: '以上都是', Option: 'D' }
      ],
      Answer: 'D',
      Analysis: 'JavaScript有多种基本数据类型，包括string、number、boolean等。',
      KnowledgePoints: [
        { Content: 'JavaScript基础', Id: 'kp1', Level: 1 }
      ],
      Chapters: [
        { ChapterName: '第一章', Id: 'ch1' }
      ]
    }
  }

  describe('receiveTransform', () => {
    it('应该正确转换单选题数据', () => {
      const result = QuestionDataConverter.receiveTransform(mockApiData)
      
      expect(result).toMatchObject({
        componentsName: 'SingleChoice',
        typeText: '单选题',
        typeId: '2',
        title: '以下哪个是JavaScript的数据类型？',
        correctAnswer: 'D',
        analysis: 'JavaScript有多种基本数据类型，包括string、number、boolean等。',
        userAnswer: null
      })
      
      expect(result.options).toHaveLength(4)
      expect(result.options?.[0]).toEqual({ label: 'string', value: 'A' })
      expect(result.knowledgePoints).toHaveLength(1)
      expect(result.chapters).toHaveLength(1)
      expect(result.id).toBeDefined()
    })

    it('应该正确处理判断题', () => {
      const judgeData = {
        ...mockApiData,
        Question: {
          ...mockApiData.Question,
          QuestionTypeId: '11',
          QuestionType: '判断题',
          Options: null
        }
      }
      
      const result = QuestionDataConverter.receiveTransform(judgeData)
      
      expect(result.componentsName).toBe('TrueFalse')
      expect(result.options).toHaveLength(2)
      expect(result.options?.[0]).toEqual({ label: '正确', value: 'A' })
      expect(result.options?.[1]).toEqual({ label: '错误', value: 'B' })
    })

    it('应该正确处理多选题', () => {
      const multipleData = {
        ...mockApiData,
        Question: {
          ...mockApiData.Question,
          QuestionTypeId: '10',
          QuestionType: '多选题',
          Answer: 'A,B,C'
        }
      }
      
      const result = QuestionDataConverter.receiveTransform(multipleData)
      
      expect(result.componentsName).toBe('MultipleChoice')
      expect(result.correctAnswer).toBe('A,B,C')
      expect(result.userAnswer).toEqual([])
    })

    it('应该正确处理填空题', () => {
      const fillBlankData = {
        ...mockApiData,
        Question: {
          ...mockApiData.Question,
          QuestionTypeId: '4',
          QuestionType: '填空题',
          Options: null
        }
      }
      
      const result = QuestionDataConverter.receiveTransform(fillBlankData)
      
      expect(result.componentsName).toBe('FillBlank')
      expect(result.options).toBeNull()
    })
  })

  describe('submitTransform', () => {
    it('应该正确转换前端数据为API格式', () => {
      // 先转换为前端格式
      const frontendData = QuestionDataConverter.receiveTransform(mockApiData)
      
      // 再转换回API格式
      const result = QuestionDataConverter.submitTransform(frontendData)
      
      expect(result).toMatchObject({
        Analysis: 'JavaScript有多种基本数据类型，包括string、number、boolean等。',
        Answer: 'D',
        QuestionType: '单选题',
        QuestionTypeId: '2',
        Title: '以下哪个是JavaScript的数据类型？'
      })
      
      expect(result.Options).toHaveLength(4)
      expect(result.Options?.[0]).toEqual({ Content: 'string', Option: 'A' })
      expect(result.KnowledgePoints).toHaveLength(1)
      expect(result.Chapters).toHaveLength(1)
    })

    it('应该正确处理多选题的答案格式', () => {
      const frontendData: Question.TransformToVoQuestionData = {
        id: 'test-id',
        componentsName: 'MultipleChoice',
        typeText: '多选题',
        typeId: '10',
        title: '测试题目',
        options: [
          { label: 'A选项', value: 'A' },
          { label: 'B选项', value: 'B' }
        ],
        correctAnswer: ['A', 'B'] as any, // 模拟数组格式的答案
        analysis: '测试解析',
        knowledgePoints: [],
        chapters: [],
        userAnswer: null
      }
      
      const result = QuestionDataConverter.submitTransform(frontendData)
      
      expect(result.Answer).toBe('A,B')
    })

    it('应该抛出错误当题目数据为空时', () => {
      expect(() => {
        QuestionDataConverter.submitTransform(null as any)
      }).toThrow('题目数据不能为空')
    })
  })

  describe('batchSubmitTransform', () => {
    it('应该正确批量转换题目数据', () => {
      const frontendData1 = QuestionDataConverter.receiveTransform(mockApiData)
      const frontendData2 = QuestionDataConverter.receiveTransform({
        ...mockApiData,
        Question: {
          ...mockApiData.Question,
          Title: '另一个测试题目'
        }
      })
      
      const results = QuestionDataConverter.batchSubmitTransform([frontendData1, frontendData2])
      
      expect(results).toHaveLength(2)
      expect(results[0].Title).toBe('以下哪个是JavaScript的数据类型？')
      expect(results[1].Title).toBe('另一个测试题目')
    })

    it('应该抛出错误当输入不是数组时', () => {
      expect(() => {
        QuestionDataConverter.batchSubmitTransform('not an array' as any)
      }).toThrow('题目数据必须是数组格式')
    })
  })
})
