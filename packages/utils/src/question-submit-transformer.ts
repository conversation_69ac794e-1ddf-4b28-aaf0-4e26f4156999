/**
 * 题目数据提交转换器
 * 专注于将前端组件数据转换为API所需格式
 */
import type {
  GeneratedQuestion,
  QuestionChapter,
  QuestionKnowledgePoints,
  QuestionOption,
} from './question-types'
import {
  QuestionTypeId,
} from './question-types'

/**
 * 题目数据提交转换器类
 * 负责将前端组件数据转换为API所需格式
 */
export class QuestionSubmitTransformer {
  /**
   * 主要转换方法：将前端组件数据转换为API格式
   * @param question 前端组件数据
   * @returns 转换后的API格式数据
   */
  public static transform(question: Question.TransformToVoQuestionData): GeneratedQuestion {
    if (!question) {
      throw new Error('题目数据不能为空')
    }

    // 根据题型ID选择不同的转换方法
    switch (question.typeId) {
      case QuestionTypeId.SINGLE_CHOICE:
        return this.convertSingleChoiceToApi(question)
      case QuestionTypeId.TRUE_FALSE:
        return this.convertTrueFalseToApi(question)
      case QuestionTypeId.MULTIPLE_CHOICE:
        return this.convertMultipleChoiceToApi(question)
      case QuestionTypeId.FILL_BLANK:
        return this.convertFillBlankToApi(question)
      default:
        return this.convertSingleChoiceToApi(question)
    }
  }

  /**
   * 将单选题转换为API格式
   * @param question 前端单选题数据
   * @returns API格式的单选题数据
   */
  private static convertSingleChoiceToApi(question: Question.TransformToVoQuestionData): GeneratedQuestion {
    // 转换选项格式
    const options: QuestionOption[] | undefined = question.options?.map(option => ({
      Content: option.label || '',
      Option: option.value || '',
    }))

    return {
      Analysis: question.analysis || '',
      Answer: question.correctAnswer || '',
      Chapters: this.convertChapters(question),
      KnowledgePoints: this.convertKnowledgePoints(question),
      Options: options || null,
      QuestionType: question.typeText || '',
      QuestionTypeId: question.typeId || '',
      Title: question.title || '',
    }
  }

  /**
   * 将判断题转换为API格式
   * @param question 前端判断题数据
   * @returns API格式的判断题数据
   */
  private static convertTrueFalseToApi(question: Question.TransformToVoQuestionData): GeneratedQuestion {
    // 判断题转换与单选题类似
    return this.convertSingleChoiceToApi(question)
  }

  /**
   * 将多选题转换为API格式
   * @param question 前端多选题数据
   * @returns API格式的多选题数据
   */
  private static convertMultipleChoiceToApi(question: Question.TransformToVoQuestionData): GeneratedQuestion {
    const result = this.convertSingleChoiceToApi(question)

    // 多选题特定处理，确保Answer格式正确（数组转为逗号分隔字符串）
    if (Array.isArray(question.correctAnswer)) {
      result.Answer = question.correctAnswer.join(',')
    }
    else if (Array.isArray(question.userAnswer) && !question.correctAnswer) {
      // 如果没有正确答案但有用户答案，使用用户答案
      result.Answer = question.userAnswer.join(',')
    }

    return result
  }

  /**
   * 将填空题转换为API格式
   * @param question 前端填空题数据
   * @returns API格式的填空题数据
   */
  private static convertFillBlankToApi(question: Question.TransformToVoQuestionData): GeneratedQuestion {
    const result = this.convertSingleChoiceToApi(question)

    // 填空题没有选项
    result.Options = null

    return result
  }

  /**
   * 转换知识点格式
   * @param question 前端题目数据
   * @returns API格式的知识点数据
   */
  private static convertKnowledgePoints(question: Question.TransformToVoQuestionData): QuestionKnowledgePoints[] | null {
    return question.knowledgePoints && question.knowledgePoints.length > 0
      ? question.knowledgePoints.map((point, index) => ({
          Content: point.Content || '',
          Id: point.Id || `kp_${question.id}_${index}`,
          Level: point.Level || 1,
        }))
      : null
  }

  /**
   * 转换章节格式
   * @param question 前端题目数据
   * @returns API格式的章节数据
   */
  private static convertChapters(question: Question.TransformToVoQuestionData): QuestionChapter[] | null {
    return question.chapters && question.chapters.length > 0
      ? question.chapters.map(chapter => ({
          ChapterName: chapter.ChapterName || '',
          Id: chapter.Id || '',
        }))
      : null
  }

  /**
   * 批量转换前端组件数据
   * @param questions 前端组件数据数组
   * @returns 转换后的API格式数据数组
   */
  public static batchTransform(questions: Question.TransformToVoQuestionData[]): GeneratedQuestion[] {
    if (!Array.isArray(questions)) {
      throw new TypeError('题目数据必须是数组格式')
    }

    return questions.map(question => this.transform(question))
  }

  /**
   * 验证前端组件数据格式
   * @param question 前端组件数据
   * @returns 是否为有效格式
   */
  public static validateFrontendData(question: any): question is Question.TransformToVoQuestionData {
    return (
      question
      && typeof question === 'object'
      && typeof question.id === 'string'
      && typeof question.typeId === 'string'
      && typeof question.title === 'string'
    )
  }

  /**
   * 安全转换：带验证的转换方法
   * @param question 前端组件数据
   * @returns 转换后的API格式数据，如果数据无效则返回null
   */
  public static safeTransform(question: any): GeneratedQuestion | null {
    if (!this.validateFrontendData(question)) {
      console.warn('Invalid frontend data format:', question)
      return null
    }

    try {
      return this.transform(question)
    }
    catch (error) {
      console.error('Error transforming frontend data:', error)
      return null
    }
  }
}
