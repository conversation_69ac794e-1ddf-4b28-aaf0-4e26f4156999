/**
 * 题型数据处理工具
 * 用于处理流式数据并转换为组件需要的格式
 */
import { nanoid } from '@sa/utils'

// ============= 通用类型定义 =============

/**
 * 通用的生成题目接口
 * 兼容QuestionsApi.GeneratedQuestion格式
 */
export interface GeneratedQuestion {
  /** 答案解析 */
  Analysis: string
  /** 正确答案 */
  Answer: string
  /** 关联的章节列表 */
  Chapters: QuestionChapter[] | null
  /** 关联的知识点列表 */
  KnowledgePoints: QuestionKnowledgePoints[] | null
  /** 选项 */
  Options: QuestionOption[] | null
  /** 题型 */
  QuestionType: string
  /** 题型ID */
  QuestionTypeId: string
  /** 题干 */
  Title: string
}

/**
 * 题目选项接口
 */
export interface QuestionOption {
  /** 选项内容 */
  Content: string
  /** 选项标识（A、B、C、D） */
  Option: string
}

/**
 * 题目关联的知识点信息
 */
export interface QuestionKnowledgePoints {
  /** 知识点内容 */
  Content: string
  /** 知识点ID */
  Id: string
  /** 知识点层级 */
  Level: number
}

/**
 * 题目关联的章节信息
 */
export interface QuestionChapter {
  /** 章节名称 */
  ChapterName: string
  /** 章节ID */
  Id: string
}

/**
 * 题目数据转换器类
 * 负责处理题目数据的接收转换和提交转换
 */
export class QuestionDataConverter {
  /**
   * 组件名称映射表
   * 将题型ID映射到对应的前端组件名称
   */
  private static readonly componentMap: Record<string, string> = {
    2: 'SingleChoice', // 单项选择题
    11: 'TrueFalse', // 判断题
    10: 'MultipleChoice', // 多项选择题
    4: 'FillBlank', // 填空题
  }

  /**
   * 获取题型对应的组件名称
   * @param typeId 题型ID
   * @returns 对应的组件名称
   */
  private static getQuestionComponentName(typeId: string): string {
    return this.componentMap[typeId] || 'SingleChoice'
  }

  /**
   * 接收转换：将API数据转换为前端组件数据
   * @param data 原始API数据
   * @returns 转换后的前端组件数据
   */
  public static receiveTransform(data: Question.QuestionData): Question.TransformToVoQuestionData {
    const questionTypeId = data.Question.QuestionTypeId

    // 根据题型ID选择不同的处理方法
    switch (questionTypeId) {
      case '2': // 单选题
        return this.processSingleChoiceData(data)
      case '11': // 判断题
        return this.processTrueFalseData(data)
      case '10': // 多选题
        return this.processMultipleChoiceData(data)
      case '4': // 填空题
        return this.processFillBlankData(data)
      default:
        return this.processSingleChoiceData(data)
    }
  }

  /**
   * 处理单选题数据
   * @param data 原始流式数据
   * @returns 处理后的单选题数据
   */
  private static processSingleChoiceData(data: Question.QuestionData): Question.TransformToVoQuestionData {
    const {
      QuestionType: typeText,
      QuestionTypeId: typeId,
      Title: title,
      Options = [],
      Answer: correctAnswer,
      Analysis,
      KnowledgePoints,
      Chapters,
    } = data.Question

    return {
      id: nanoid(),
      componentsName: this.getQuestionComponentName(typeId),
      typeText,
      typeId,
      title,
      options: Options?.map(option => ({
        label: option.Content,
        value: option.Option,
      })) || null,
      correctAnswer,
      analysis: Analysis,
      knowledgePoints: KnowledgePoints || [],
      chapters: Chapters || [],
      userAnswer: null,
    }
  }

  /**
   * 处理判断题数据
   * @param data 原始流式数据
   * @returns 处理后的判断题数据
   */
  private static processTrueFalseData(data: Question.QuestionData): Question.TransformToVoQuestionData {
    // 判断题处理逻辑与单选题类似，但可以添加特定的处理
    const result = this.processSingleChoiceData(data)

    // 确保判断题只有两个选项：对/错
    if (!result.options || result.options.length === 0) {
      result.options = [
        { label: '正确', value: 'A' },
        { label: '错误', value: 'B' },
      ]
    }

    return result
  }

  /**
   * 处理多选题数据
   * @param data 原始流式数据
   * @returns 处理后的多选题数据
   */
  private static processMultipleChoiceData(data: Question.QuestionData): Question.TransformToVoQuestionData {
    const result = this.processSingleChoiceData(data)

    // 多选题特定处理，例如确保correctAnswer是数组格式
    if (typeof result.correctAnswer === 'string' && result.correctAnswer.includes(',')) {
      result.userAnswer = [] // 多选题的用户答案初始化为空数组
    }

    return result
  }

  /**
   * 处理填空题数据
   * @param data 原始流式数据
   * @returns 处理后的填空题数据
   */
  private static processFillBlankData(data: Question.QuestionData): Question.TransformToVoQuestionData {
    const result = this.processSingleChoiceData(data)

    // 填空题没有选项
    result.options = null

    return result
  }

  /**
   * 提交转换：将前端组件数据转换为API所需格式
   * @param question 前端组件数据
   * @returns 转换后的API数据
   */
  public static submitTransform(question: Question.TransformToVoQuestionData): GeneratedQuestion {
    if (!question) {
      throw new Error('题目数据不能为空')
    }

    // 根据题型ID选择不同的提交转换方法
    switch (question.typeId) {
      case '2': // 单选题
        return this.convertSingleChoiceToApi(question)
      case '11': // 判断题
        return this.convertTrueFalseToApi(question)
      case '10': // 多选题
        return this.convertMultipleChoiceToApi(question)
      case '4': // 填空题
        return this.convertFillBlankToApi(question)
      default:
        return this.convertSingleChoiceToApi(question)
    }
  }

  /**
   * 将单选题转换为API格式
   * @param question 前端单选题数据
   * @returns API格式的单选题数据
   */
  private static convertSingleChoiceToApi(question: Question.TransformToVoQuestionData): GeneratedQuestion {
    // 转换选项格式
    const options: QuestionOption[] | undefined = question.options?.map(option => ({
      Content: option.label || '',
      Option: option.value || '',
    }))

    // 转换知识点格式
    const knowledgePoints = this.convertKnowledgePoints(question)

    return {
      Analysis: question.analysis || '',
      Answer: question.correctAnswer || '',
      Chapters: this.convertChapters(question),
      KnowledgePoints: knowledgePoints,
      Options: options || null,
      QuestionType: question.typeText || '',
      QuestionTypeId: question.typeId || '',
      Title: question.title || '',
    }
  }

  /**
   * 将判断题转换为API格式
   * @param question 前端判断题数据
   * @returns API格式的判断题数据
   */
  private static convertTrueFalseToApi(question: Question.TransformToVoQuestionData): GeneratedQuestion {
    // 判断题转换与单选题类似
    return this.convertSingleChoiceToApi(question)
  }

  /**
   * 将多选题转换为API格式
   * @param question 前端多选题数据
   * @returns API格式的多选题数据
   */
  private static convertMultipleChoiceToApi(question: Question.TransformToVoQuestionData): GeneratedQuestion {
    const result = this.convertSingleChoiceToApi(question)

    // 多选题特定处理，例如确保Answer格式正确
    if (Array.isArray(question.correctAnswer)) {
      result.Answer = question.correctAnswer.join(',')
    }

    return result
  }

  /**
   * 将填空题转换为API格式
   * @param question 前端填空题数据
   * @returns API格式的填空题数据
   */
  private static convertFillBlankToApi(question: Question.TransformToVoQuestionData): GeneratedQuestion {
    const result = this.convertSingleChoiceToApi(question)

    // 填空题没有选项
    result.Options = null

    return result
  }

  /**
   * 转换知识点格式
   * @param question 前端题目数据
   * @returns API格式的知识点数据
   */
  private static convertKnowledgePoints(question: Question.TransformToVoQuestionData): QuestionKnowledgePoints[] | null {
    return question.knowledgePoints && question.knowledgePoints.length > 0
      ? question.knowledgePoints.map((point, index) => ({
          Content: point.Content || '',
          Id: point.Id || `kp_${question.id}_${index}`,
          Level: point.Level || 1,
        }))
      : null
  }

  /**
   * 转换章节格式
   * @param question 前端题目数据
   * @returns API格式的章节数据
   */
  private static convertChapters(question: Question.TransformToVoQuestionData): QuestionChapter[] | null {
    return question.chapters && question.chapters.length > 0
      ? question.chapters.map(chapter => ({
          ChapterName: chapter.ChapterName || '',
          Id: chapter.Id || '',
        }))
      : null
  }

  /**
   * 批量转换题目数据（提交转换）
   * @param questions 前端题目数据数组
   * @returns 转换后的API格式数据数组
   */
  public static batchSubmitTransform(questions: Question.TransformToVoQuestionData[]): GeneratedQuestion[] {
    if (!Array.isArray(questions)) {
      throw new TypeError('题目数据必须是数组格式')
    }

    return questions.map(question => this.submitTransform(question))
  }
}

// ============= 向后兼容的导出函数 =============

/**
 * 主要的数据处理函数（向后兼容）
 * 根据题型ID处理不同类型的题目数据
 * @deprecated 请使用 QuestionDataConverter.receiveTransform 替代
 */
export function processQuestionData(data: Question.QuestionData): Question.TransformToVoQuestionData {
  return QuestionDataConverter.receiveTransform(data)
}

/**
 * 将前端题目数据转换为API所需的GeneratedQuestion格式（向后兼容）
 * @deprecated 请使用 QuestionDataConverter.submitTransform 替代
 */
export function convertToGeneratedQuestion(question: Question.TransformToVoQuestionData): GeneratedQuestion {
  return QuestionDataConverter.submitTransform(question)
}

/**
 * 批量转换题目数据（向后兼容）
 * @deprecated 请使用 QuestionDataConverter.batchSubmitTransform 替代
 */
export function convertQuestionsToGeneratedQuestions(questions: Question.TransformToVoQuestionData[]): GeneratedQuestion[] {
  return QuestionDataConverter.batchSubmitTransform(questions)
}
